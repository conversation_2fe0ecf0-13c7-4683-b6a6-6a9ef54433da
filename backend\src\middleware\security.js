const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const mongoSanitize = require('express-mongo-sanitize');
const logger = require('../utils/logger');
const crypto = require('crypto');

const security = {
  // Rate limiting configuration
  rateLimiter: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later',
    handler: (req, res) => {
      logger.warn(`Rate limit exceeded for IP: ${req.ip}`);
      res.status(429).json({
        error: 'Too many requests',
        retryAfter: Math.ceil(req.rateLimit.resetTime / 1000)
      });
    }
  }),

  // Input sanitization middleware
  sanitizeInput: (req, res, next) => {
    // Sanitize request body, query, and params
    req.body = mongoSanitize.sanitize(req.body);
    req.query = mongoSanitize.sanitize(req.query);
    req.params = mongoSanitize.sanitize(req.params);
    next();
  },

  // Request ID middleware for tracking
  requestId: (req, res, next) => {
    req.id = crypto.randomUUID();
    res.setHeader('X-Request-ID', req.id);
    next();
  },

  // Security headers middleware
  securityHeaders: (req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    next();
  },

  // Request logging
  requestLogger: (req, res, next) => {
    logger.info(`${req.method} ${req.path} - IP: ${req.ip}`);
    next();
  },

  // Error handling
  errorHandler: (err, req, res, next) => {
    logger.error('Unhandled error:', err);
    
    // Don't leak error details in production
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    res.status(err.status || 500).json({
      success: false,
      message: err.message || 'Internal server error',
      ...(isDevelopment && { stack: err.stack }),
      requestId: req.id,
      timestamp: new Date().toISOString()
    });
  }
};

// Configure security middleware
const configureSecurityMiddleware = (app) => {
  // Basic security setup
  app.use(security.requestId); // Add request IDs for tracking
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  })); // Secure HTTP headers
  
  app.use(security.securityHeaders); // Additional security headers
  app.use(security.sanitizeInput); // Input sanitization
  app.use(mongoSanitize()); // Prevent NoSQL injection
  app.use(security.requestLogger); // Request logging
  app.use(security.rateLimiter); // Rate limiting
  
  // Error handler should be last
  app.use(security.errorHandler);
};

module.exports = {
  security,
  configureSecurityMiddleware
};