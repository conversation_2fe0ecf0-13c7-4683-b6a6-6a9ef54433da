/**
 * Authentication API Routes
 * 
 * This module provides secure API routes for authentication with proper JWT implementation.
 */

const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const rateLimit = require('express-rate-limit');
const { authenticateUser, hashPassword, verifyToken, generateToken } = require('../../services/authService');
const { authenticate, authorize } = require('../../middleware/authMiddleware');
const { validateBody, schemas } = require('../../middleware/validationMiddleware');
const logger = require('../../utils/logger');
const Joi = require('joi');
const mongoSanitize = require('express-mongo-sanitize');

// Rate limiting for authentication routes
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs for login
  message: 'Too many authentication attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
});

const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 registration attempts per hour
  message: 'Too many registration attempts, please try again later',
});

// Validation schemas
const loginSchema = Joi.object({
  username: Joi.string().trim().min(3).max(50).required().messages({
    'string.empty': 'Username is required',
    'string.min': 'Username must be at least 3 characters',
    'string.max': 'Username cannot exceed 50 characters',
    'any.required': 'Username is required'
  }),
  password: Joi.string().min(6).required().messages({
    'string.empty': 'Password is required',
    'string.min': 'Password must be at least 6 characters',
    'any.required': 'Password is required'
  })
});

const registerSchema = Joi.object({
  username: Joi.string().trim().min(3).max(50).required().pattern(/^[a-zA-Z0-9_]+$/).messages({
    'string.empty': 'Username is required',
    'string.min': 'Username must be at least 3 characters',
    'string.max': 'Username cannot exceed 50 characters',
    'string.pattern.base': 'Username can only contain letters, numbers, and underscores',
    'any.required': 'Username is required'
  }),
  email: Joi.string().email().required().messages({
    'string.empty': 'Email is required',
    'string.email': 'Email must be valid',
    'any.required': 'Email is required'
  }),
  password: Joi.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).required().messages({
    'string.empty': 'Password is required',
    'string.min': 'Password must be at least 8 characters',
    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    'any.required': 'Password is required'
  }),
  firstName: Joi.string().trim().min(1).max(50).required().messages({
    'string.empty': 'First name is required',
    'string.max': 'First name cannot exceed 50 characters',
    'any.required': 'First name is required'
  }),
  lastName: Joi.string().trim().min(1).max(50).required().messages({
    'string.empty': 'Last name is required',
    'string.max': 'Last name cannot exceed 50 characters',
    'any.required': 'Last name is required'
  })
});

const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required().messages({
    'string.empty': 'Current password is required',
    'any.required': 'Current password is required'
  }),
  newPassword: Joi.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).required().messages({
    'string.empty': 'New password is required',
    'string.min': 'New password must be at least 8 characters',
    'string.pattern.base': 'New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    'any.required': 'New password is required'
  })
});

/**
 * @route POST /api/auth/login
 * @desc Login user with proper JWT authentication
 * @access Public
 */
router.post('/login', authLimiter, validateBody(loginSchema), async (req, res, next) => {
  try {
    const { username, password } = req.body;

    logger.info(`Login attempt for username: ${username} from IP: ${req.ip}`);

    // Use the proper authentication service
    const authResult = await authenticateUser(username, password);

    if (!authResult.success) {
      logger.warn(`Failed login attempt for username: ${username} from IP: ${req.ip} - ${authResult.message}`);
      return res.status(401).json({
        success: false,
        message: authResult.message
      });
    }

    logger.info(`Successful login for username: ${username} from IP: ${req.ip}`);

    // Return user data and token (sensitive data already filtered in authService)
    res.json({
      success: true,
      message: 'Login successful',
      token: authResult.token,
      user: authResult.user
    });

  } catch (error) {
    logger.error(`Login error for username: ${req.body.username || 'unknown'} from IP: ${req.ip}:`, error);
    res.status(500).json({
      success: false,
      message: 'Authentication service unavailable'
    });
  }
});

/**
 * @route POST /api/auth/register
 * @desc Register user with proper password hashing and validation
 * @access Public
 */
router.post('/register', registerLimiter, validateBody(registerSchema), async (req, res, next) => {
  try {
    const { username, email, password, firstName, lastName } = req.body;

    logger.info(`Registration attempt for username: ${username}, email: ${email} from IP: ${req.ip}`);

    // Check if MongoDB is available for registration
    const mongodb = require('../../config/mongodb');
    
    try {
      const { db } = await mongodb.connectDB();
      
      // Check if user already exists
      const existingUser = await db.collection('users').findOne({
        $or: [{ username }, { email }]
      });

      if (existingUser) {
        logger.warn(`Registration failed - user exists: ${username} from IP: ${req.ip}`);
        return res.status(409).json({
          success: false,
          message: existingUser.username === username ? 'Username already exists' : 'Email already exists'
        });
      }

      // Hash the password
      const hashedPassword = await hashPassword(password);

      // Create new user
      const newUser = {
        username,
        email,
        password: hashedPassword,
        firstName,
        lastName,
        role: 'beta', // Default role for new registrations
        status: 'active',
        isActive: true,
        subscriptionTier: 'Beta Access',
        modules: ['dashboard', 'animals', 'health', 'feeding', 'financial', 'settings'],
        permissions: ['basic_features', 'limited_access'],
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLogin: null
      };

      const result = await db.collection('users').insertOne(newUser);

      logger.info(`User registered successfully: ${username} from IP: ${req.ip}`);

      // Generate token for immediate login
      const token = generateToken({
        id: result.insertedId.toString(),
        username: newUser.username,
        role: newUser.role,
        firstName: newUser.firstName,
        lastName: newUser.lastName
      });

      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        token,
        user: {
          id: result.insertedId.toString(),
          username: newUser.username,
          email: newUser.email,
          firstName: newUser.firstName,
          lastName: newUser.lastName,
          role: newUser.role,
          subscriptionTier: newUser.subscriptionTier,
          modules: newUser.modules,
          permissions: newUser.permissions
        }
      });

    } catch (dbError) {
      logger.error(`Database error during registration for ${username}:`, dbError);
      return res.status(503).json({
        success: false,
        message: 'Registration service temporarily unavailable'
      });
    }

  } catch (error) {
    logger.error(`Registration error for username: ${req.body.username || 'unknown'} from IP: ${req.ip}:`, error);
    res.status(500).json({
      success: false,
      message: 'Registration failed'
    });
  }
});

/**
 * @route GET /api/auth/me
 * @desc Get current user profile
 * @access Private
 */
router.get('/me', authenticate, async (req, res, next) => {
  try {
    logger.info(`Profile request for user: ${req.user.username} from IP: ${req.ip}`);

    // Return user data (password already excluded by JWT token)
    res.json({
      success: true,
      data: {
        id: req.user.id,
        username: req.user.username,
        firstName: req.user.firstName,
        lastName: req.user.lastName,
        role: req.user.role,
        permissions: req.user.permissions || [],
        lastLogin: req.user.lastLogin,
        createdAt: req.user.createdAt,
        updatedAt: req.user.updatedAt
      }
    });
  } catch (error) {
    logger.error(`Profile error for user: ${req.user?.username || 'unknown'} from IP: ${req.ip}:`, error);
    res.status(500).json({
      success: false,
      message: 'Profile service unavailable'
    });
  }
});

/**
 * @route POST /api/auth/change-password
 * @desc Change user password with proper validation
 * @access Private
 */
router.post('/change-password', authenticate, validateBody(changePasswordSchema), async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    logger.info(`Password change request for user: ${req.user.username} from IP: ${req.ip}`);

    const mongodb = require('../../config/mongodb');
    
    try {
      const { db } = await mongodb.connectDB();
      
      // Get current user
      const user = await db.collection('users').findOne({ _id: new mongodb.ObjectId(userId) });
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Verify current password
      const { comparePassword } = require('../../services/authService');
      const isCurrentPasswordValid = await comparePassword(currentPassword, user.password);
      
      if (!isCurrentPasswordValid) {
        logger.warn(`Invalid current password for user: ${req.user.username} from IP: ${req.ip}`);
        return res.status(401).json({
          success: false,
          message: 'Current password is incorrect'
        });
      }

      // Hash new password
      const hashedNewPassword = await hashPassword(newPassword);

      // Update password
      await db.collection('users').updateOne(
        { _id: new mongodb.ObjectId(userId) },
        { 
          $set: { 
            password: hashedNewPassword,
            updatedAt: new Date()
          }
        }
      );

      logger.info(`Password changed successfully for user: ${req.user.username} from IP: ${req.ip}`);

      res.json({
        success: true,
        message: 'Password changed successfully'
      });

    } catch (dbError) {
      logger.error(`Database error during password change for ${req.user.username}:`, dbError);
      return res.status(503).json({
        success: false,
        message: 'Password change service temporarily unavailable'
      });
    }

  } catch (error) {
    logger.error(`Password change error for user: ${req.user?.username || 'unknown'} from IP: ${req.ip}:`, error);
    res.status(500).json({
      success: false,
      message: 'Password change failed'
    });
  }
});

/**
 * @route POST /api/auth/refresh-token
 * @desc Refresh JWT token
 * @access Public
 */
router.post('/refresh-token', rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 token refresh requests per windowMs
  message: 'Too many token refresh attempts, please try again later'
}), async (req, res, next) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Token is required'
      });
    }

    // Verify the current token
    const decoded = verifyToken(token);
    
    if (!decoded) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }

    // Generate new token with same payload
    const newToken = generateToken({
      id: decoded.id,
      username: decoded.username,
      role: decoded.role,
      firstName: decoded.firstName,
      lastName: decoded.lastName
    });

    logger.info(`Token refreshed for user: ${decoded.username} from IP: ${req.ip}`);

    res.json({
      success: true,
      token: newToken
    });
  } catch (error) {
    logger.error(`Token refresh error from IP: ${req.ip}:`, error);
    res.status(500).json({
      success: false,
      message: 'Token refresh failed'
    });
  }
});

/**
 * @route POST /api/auth/logout
 * @desc Logout user (client-side logout)
 * @access Private
 */
router.post('/logout', authenticate, (req, res) => {
  logger.info(`User logout: ${req.user.username} from IP: ${req.ip}`);
  
  // For JWT, logout is primarily client-side (removing token)
  // In production, you might want to maintain a blacklist of revoked tokens
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

/**
 * @route POST /api/auth/verify-token
 * @desc Verify if a token is valid
 * @access Public
 */
router.post('/verify-token', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Token is required'
      });
    }

    const decoded = verifyToken(token);
    
    if (!decoded) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }

    res.json({
      success: true,
      valid: true,
      user: {
        id: decoded.id,
        username: decoded.username,
        role: decoded.role,
        firstName: decoded.firstName,
        lastName: decoded.lastName
      }
    });
  } catch (error) {
    logger.error(`Token verification error from IP: ${req.ip}:`, error);
    res.status(500).json({
      success: false,
      message: 'Token verification failed'
    });
  }
});

module.exports = router;
