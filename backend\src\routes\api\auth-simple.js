/**
 * Simple Authentication API Routes (without rate limiting)
 */

const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const { authenticateUser, hashPassword, verifyToken, generateToken } = require('../../services/authService');
const { authenticate, authorize } = require('../../middleware/authMiddleware');
const { validateBody, schemas } = require('../../middleware/validationMiddleware');
const logger = require('../../utils/logger');
const Joi = require('joi');
const mongoSanitize = require('express-mongo-sanitize');

// Validation schemas
const loginSchema = Joi.object({
  username: Joi.string().trim().min(3).max(50).required().messages({
    'string.empty': 'Username is required',
    'string.min': 'Username must be at least 3 characters',
    'string.max': 'Username cannot exceed 50 characters',
    'any.required': 'Username is required'
  }),
  password: Joi.string().min(6).required().messages({
    'string.empty': 'Password is required',
    'string.min': 'Password must be at least 6 characters',
    'any.required': 'Password is required'
  })
});

/**
 * @route POST /api/auth/login
 * @desc Login user with proper JWT authentication
 * @access Public
 */
router.post('/login', validateBody(loginSchema), async (req, res, next) => {
  try {
    const { username, password } = req.body;

    logger.info(`Login attempt for username: ${username} from IP: ${req.ip}`);

    // Use the proper authentication service
    const authResult = await authenticateUser(username, password);

    if (!authResult.success) {
      logger.warn(`Failed login attempt for username: ${username} from IP: ${req.ip} - ${authResult.message}`);
      return res.status(401).json({
        success: false,
        message: authResult.message
      });
    }

    logger.info(`Successful login for username: ${username} from IP: ${req.ip}`);

    // Return user data and token (sensitive data already filtered in authService)
    res.json({
      success: true,
      message: 'Login successful',
      token: authResult.token,
      user: authResult.user
    });

  } catch (error) {
    logger.error(`Login error for username: ${req.body.username || 'unknown'} from IP: ${req.ip}:`, error);
    res.status(500).json({
      success: false,
      message: 'Authentication service unavailable'
    });
  }
});

/**
 * @route GET /api/auth/me
 * @desc Get current user profile
 * @access Private
 */
router.get('/me', authenticate, async (req, res, next) => {
  try {
    logger.info(`Profile request for user: ${req.user.username} from IP: ${req.ip}`);

    // Return user data (password already excluded by JWT token)
    res.json({
      success: true,
      data: {
        id: req.user.id,
        username: req.user.username,
        firstName: req.user.firstName,
        lastName: req.user.lastName,
        role: req.user.role,
        permissions: req.user.permissions || [],
        lastLogin: req.user.lastLogin,
        createdAt: req.user.createdAt,
        updatedAt: req.user.updatedAt
      }
    });
  } catch (error) {
    logger.error(`Profile error for user: ${req.user?.username || 'unknown'} from IP: ${req.ip}:`, error);
    res.status(500).json({
      success: false,
      message: 'Profile service unavailable'
    });
  }
});

/**
 * @route POST /api/auth/logout
 * @desc Logout user (client-side logout)
 * @access Private
 */
router.post('/logout', authenticate, (req, res) => {
  logger.info(`User logout: ${req.user.username} from IP: ${req.ip}`);
  
  // For JWT, logout is primarily client-side (removing token)
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

module.exports = router;
