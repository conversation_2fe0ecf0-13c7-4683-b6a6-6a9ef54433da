import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Button,
  Box,
  Chip,
  CircularProgress,
  Tabs,
  Tab
} from '@mui/material';
import PlayArrow from '@mui/icons-material/PlayArrow';
import Pets from '@mui/icons-material/Pets';
import LocalHospital from '@mui/icons-material/LocalHospital';
import TrendingUp from '@mui/icons-material/TrendingUp';


import AccountBalance from '@mui/icons-material/AccountBalance';
import Restaurant from '@mui/icons-material/Restaurant';
import Memory from '@mui/icons-material/Memory';
import WhatsApp from '@mui/icons-material/WhatsApp';
import { motion } from 'framer-motion';
// Removed NeuralNetworkBackground for simplified Demo design
import HolographicCard from '../components/effects/HolographicCard';
import EnhancedLanguageSelector from '../components/common/EnhancedLanguageSelector';
import AgriIntelLogo from '../components/common/AgriIntelLogo';

// Import comprehensive landing page sections
import TestimonialsSection from '../components/landing/TestimonialsSection';
import FAQSection from '../components/landing/FAQSection';
import TrustSignalsSection from '../components/landing/TrustSignalsSection';

// Landing page sections are now inline for single-page design


// Import only existing styles
import '../styles/agriintel-landing.css';

// TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  // Define background CSS classes for each tab
  const getTabClassName = (tabIndex: number) => {
    const tabClasses = [
      'agri-tab-home-bg',      // Home Tab - Beautiful Cattle Scene
      'agri-tab-features-bg',  // Features Tab - Professional Cattle
      'agri-tab-testimonials-bg', // Testimonials Tab - Diverse Livestock
      'agri-tab-trust-bg',     // Trust & Security Tab - Health Monitoring
      'agri-tab-faq-bg',       // FAQ Tab - Farm Operations
      'agri-tab-contact-bg'    // Contact Tab - Livestock Management
    ];
    return tabClasses[tabIndex] || tabClasses[0];
  };

  const tabClassName = getTabClassName(index);

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`agriintel-tabpanel-${index}`}
      aria-labelledby={`agriintel-tab-${index}`}
      className={`agriintel-tabpanel agri-tabpanel ${tabClassName}`}
      style={{
        // Force background images to be visible with inline styles as backup
        backgroundImage: index === 0 ? `linear-gradient(135deg, rgba(21, 101, 192, 0.3) 0%, rgba(46, 125, 50, 0.25) 25%, rgba(76, 175, 80, 0.2) 50%, rgba(245, 124, 0, 0.25) 75%, rgba(46, 125, 50, 0.3) 100%), url('/images/AgriIntel-Livestock-Solutions/herd-cows-grazing-pasture-daytime.jpg')` :
                         index === 1 ? `linear-gradient(135deg, rgba(21, 101, 192, 0.3) 0%, rgba(46, 125, 50, 0.25) 25%, rgba(76, 175, 80, 0.2) 50%, rgba(245, 124, 0, 0.25) 75%, rgba(46, 125, 50, 0.3) 100%), url('/images/animals/premium-cattle-1.jpg')` :
                         index === 2 ? `linear-gradient(135deg, rgba(21, 101, 192, 0.3) 0%, rgba(46, 125, 50, 0.25) 25%, rgba(76, 175, 80, 0.2) 50%, rgba(245, 124, 0, 0.25) 75%, rgba(46, 125, 50, 0.3) 100%), url('/images/gallery/livestock-management.jpg')` :
                         index === 3 ? `linear-gradient(135deg, rgba(21, 101, 192, 0.3) 0%, rgba(46, 125, 50, 0.25) 25%, rgba(76, 175, 80, 0.2) 50%, rgba(245, 124, 0, 0.25) 75%, rgba(46, 125, 50, 0.3) 100%), url('/images/landing-backgrounds/high-tech-pasture.jpg')` :
                         index === 4 ? `linear-gradient(135deg, rgba(21, 101, 192, 0.3) 0%, rgba(46, 125, 50, 0.25) 25%, rgba(76, 175, 80, 0.2) 50%, rgba(245, 124, 0, 0.25) 75%, rgba(46, 125, 50, 0.3) 100%), url('/images/gallery/farm-operations-1.jpg')` :
                         `linear-gradient(135deg, rgba(21, 101, 192, 0.3) 0%, rgba(46, 125, 50, 0.25) 25%, rgba(76, 175, 80, 0.2) 50%, rgba(245, 124, 0, 0.25) 75%, rgba(46, 125, 50, 0.3) 100%), url('/images/animals/premium-cattle-2.jpg')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed',
        backgroundRepeat: 'no-repeat',
        minHeight: '100vh'
      }}
      {...other}
    >
      {value === index && (
        <Box
          className="agriintel-tabpanel-content"
          sx={{
            height: '100vh',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '20px 15px',
            background: 'transparent', // Remove white background
            borderRadius: 0, // Remove border radius
            margin: 0, // Remove margin
            border: 'none', // Remove border
            boxShadow: 'none' // Remove box shadow
          }}
        >
          <Container
            maxWidth="lg"
            sx={{
              height: 'auto',
              maxHeight: '80vh',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              overflow: 'hidden'
            }}
          >
            {children}
          </Container>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `agriintel-tab-${index}`,
    'aria-controls': `agriintel-tabpanel-${index}`,
  };
}

const AgriIntelLanding: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);



  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handlePlanSelect = async (plan: string) => {
    setIsLoading(true);
    setLoadingPlan(plan);

    try {
      await new Promise(resolve => setTimeout(resolve, 1500));

      if (plan === 'beta') {
        navigate('/login?tier=beta');
      } else if (plan === 'professional') {
        navigate('/login?tier=professional');
      } else if (plan === 'enterprise') {
        navigate('/login?tier=enterprise');
      }
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsLoading(false);
      setLoadingPlan(null);
    }
  };

  return (
    <main className="agri-landing-main" role="main">
      {/* REMOVED: Background images - Using pure gradient themes only */}
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'linear-gradient(135deg, rgba(21, 101, 192, 0.95) 0%, rgba(46, 125, 50, 0.90) 50%, rgba(245, 124, 0, 0.85) 100%)',
          zIndex: -1
        }}
      />

      {/* Professional Clean Header */}
      <motion.header
        className="agri-professional-header"
        role="banner"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Container maxWidth="xl">
          <div className="agri-header-clean">
            <div className="agri-logo-section">
              <AgriIntelLogo
                variant="full"
                size="large"
                theme="professional"
                showTagline={false}
                orientation="horizontal"
              />
              <Typography variant="h6" className="agri-clean-tagline">
                Smart Farming, Smarter Decisions
              </Typography>
            </div>

            <div className="agri-header-right">
              <EnhancedLanguageSelector
                variant="compact"
                showLabel={false}
                showFlag={true}
                size="small"
              />
            </div>
          </div>
        </Container>
      </motion.header>

      {/* Quick Navigation Helper */}
      <div className="agri-quick-nav">
        <button
          className={tabValue === 0 ? 'active' : ''}
          onClick={() => setTabValue(0)}
        >
          Home
        </button>
        <button
          className={tabValue === 1 ? 'active' : ''}
          onClick={() => setTabValue(1)}
        >
          Features
        </button>
        <button
          className={tabValue === 2 ? 'active' : ''}
          onClick={() => setTabValue(2)}
        >
          Stories
        </button>
        <button
          className={tabValue === 3 ? 'active' : ''}
          onClick={() => setTabValue(3)}
        >
          Trust
        </button>
        <button
          className={tabValue === 4 ? 'active' : ''}
          onClick={() => setTabValue(4)}
        >
          FAQ
        </button>
        <button
          className={tabValue === 5 ? 'active' : ''}
          onClick={() => setTabValue(5)}
        >
          Contact
        </button>
      </div>

      {/* Clean Professional Navigation */}
      <Box className="agri-clean-nav">
        <Container maxWidth="xl">
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="AgriIntel navigation tabs"
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                minWidth: 140,
                fontWeight: 500,
                fontSize: '16px',
                textTransform: 'none',
                color: '#666',
                padding: '12px 20px',
                margin: '0 2px',
                transition: 'all 0.3s ease',
                '&.Mui-selected': {
                  color: '#1565C0',
                  fontWeight: 600,
                  borderBottom: '2px solid #1565C0',
                },
                '&:hover': {
                  color: '#1565C0',
                  backgroundColor: 'rgba(21, 101, 192, 0.05)',
                }
              },
              '& .MuiTabs-indicator': {
                display: 'none', // Hide default indicator since we're using custom styling
              },
              '& .MuiTabs-flexContainer': {
                gap: '8px',
                paddingBottom: '8px',
              },
              '& .MuiTabs-scroller': {
                paddingTop: '8px',
              }
            }}
          >
            <Tab label="🏠 Home" {...a11yProps(0)} />
            <Tab label="⚡ Features" {...a11yProps(1)} />
            <Tab label="💬 Testimonials" {...a11yProps(2)} />
            <Tab label="🔒 Trust & Security" {...a11yProps(3)} />
            <Tab label="❓ FAQ" {...a11yProps(4)} />
            <Tab label="📞 Contact" {...a11yProps(5)} />
          </Tabs>
        </Container>
      </Box>

      {/* FIXED Tab Content - Full Width */}
      <Box sx={{ width: '100%' }}>
        {/* Home Tab - RICH CONTENT WITH SUBSCRIPTION CARDS */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ textAlign: 'center', maxWidth: '1200px', mx: 'auto' }}>
            <Typography
              variant="h1"
              sx={{
                color: 'white',
                textShadow: '3px 3px 6px rgba(0, 0, 0, 0.9)',
                fontWeight: 900,
                fontSize: { xs: '3rem', md: '4rem' },
                mb: 2
              }}
            >
              AgriIntel
            </Typography>
            <Typography
              variant="h5"
              sx={{
                color: 'white',
                textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)',
                fontWeight: 500,
                fontSize: { xs: '1.2rem', md: '1.5rem' },
                mb: 4
              }}
            >
              Smart Livestock Management for African Farmers
            </Typography>

            {/* Subscription Cards */}
            <Box className="agri-features-horizontal-container">
              <motion.div
                className="agri-features-horizontal-scroll"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                {/* BETA Trial Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#FF9800"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #FF9800, #F57C00)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <PlayArrow style={{ fontSize: '3.5rem', color: '#FF9800' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 1,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        BETA Trial
                      </Typography>
                      <Chip
                        label="FREE 90 DAYS"
                        sx={{
                          background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)',
                          color: 'white',
                          fontWeight: 700,
                          mb: 2
                        }}
                      />
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Perfect for small farms getting started with digital livestock management
                      </Typography>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                          • Up to 49 animals
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                          • 5 core modules
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                          • Basic reporting
                        </Typography>
                      </Box>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={() => handlePlanSelect('beta')}
                        disabled={isLoading}
                        sx={{
                          background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)',
                          color: 'white',
                          fontWeight: 700,
                          py: 1.5,
                          borderRadius: '12px',
                          '&:hover': {
                            background: 'linear-gradient(135deg, #F57C00 0%, #E65100 100%)',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 8px 25px rgba(255, 152, 0, 0.4)'
                          }
                        }}
                      >
                        {isLoading && loadingPlan === 'beta' ? (
                          <CircularProgress size={24} color="inherit" />
                        ) : (
                          'Start Free Trial'
                        )}
                      </Button>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Professional Plan Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#4CAF50"
                    intensity={0.8}
                    borderGradient="linear-gradient(135deg, #4CAF50, #2E7D32)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <TrendingUp style={{ fontSize: '3.5rem', color: '#4CAF50' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 1,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Professional
                      </Typography>
                      <Chip
                        label="R699/MONTH"
                        sx={{
                          background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
                          color: 'white',
                          fontWeight: 700,
                          mb: 2
                        }}
                      />
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Complete livestock management with AI automation and advanced analytics
                      </Typography>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                          • Unlimited animals
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                          • All 12 modules
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                          • AI automation
                        </Typography>
                      </Box>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={() => handlePlanSelect('professional')}
                        disabled={isLoading}
                        sx={{
                          background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
                          color: 'white',
                          fontWeight: 700,
                          py: 1.5,
                          borderRadius: '12px',
                          '&:hover': {
                            background: 'linear-gradient(135deg, #2E7D32 0%, #1B5E20 100%)',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 8px 25px rgba(76, 175, 80, 0.4)'
                          }
                        }}
                      >
                        {isLoading && loadingPlan === 'professional' ? (
                          <CircularProgress size={24} color="inherit" />
                        ) : (
                          'Go Professional'
                        )}
                      </Button>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Enterprise Plan Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#9C27B0"
                    intensity={0.8}
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <AccountBalance style={{ fontSize: '3.5rem', color: '#9C27B0' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 1,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Enterprise
                      </Typography>
                      <Chip
                        label="CUSTOM PRICING"
                        sx={{
                          background: 'linear-gradient(135deg, #9C27B0 0%, #673AB7 100%)',
                          color: 'white',
                          fontWeight: 700,
                          mb: 2
                        }}
                      />
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Enterprise-grade solution with dedicated support and custom integrations
                      </Typography>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                          • Multi-farm management
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                          • Custom integrations
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                          • Dedicated support
                        </Typography>
                      </Box>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={() => window.open('https://wa.me/27829908204?text=Hello%20AgriIntel%20Team%2C%20I%27m%20interested%20in%20Enterprise%20solutions.', '_blank')}
                        sx={{
                          background: 'linear-gradient(135deg, #9C27B0 0%, #673AB7 100%)',
                          color: 'white',
                          fontWeight: 700,
                          py: 1.5,
                          borderRadius: '12px',
                          '&:hover': {
                            background: 'linear-gradient(135deg, #673AB7 0%, #512DA8 100%)',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 8px 25px rgba(156, 39, 176, 0.4)'
                          }
                        }}
                      >
                        Contact Sales
                      </Button>
                    </Box>
                  </HolographicCard>
                </motion.div>
              </motion.div>
            </Box>
          </Box>
        </TabPanel>



        {/* Features Tab - RICH FEATURE CARDS */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ textAlign: 'center', maxWidth: '1200px', mx: 'auto' }}>
            <Typography
              variant="h2"
              sx={{
                color: 'white',
                textShadow: '3px 3px 6px rgba(0, 0, 0, 0.9)',
                fontWeight: 800,
                fontSize: { xs: '2.5rem', md: '3rem' },
                mb: 4
              }}
            >
              Smart Features
            </Typography>

            {/* Feature Cards */}
            <Box className="agri-features-horizontal-container">
              <motion.div
                className="agri-features-horizontal-scroll"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                {/* Animal Management Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#1565C0"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #1565C0, #2196F3)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Pets style={{ fontSize: '3.5rem', color: '#1565C0' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Animal Management
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Complete livestock lifecycle management with RFID integration, breeding tracking, and performance analytics
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Digital Profiles" className="agri-feature-chip" />
                        <Chip label="RFID Integration" className="agri-feature-chip" />
                        <Chip label="Breeding Management" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Health Management Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#4CAF50"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #4CAF50, #2E7D32)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <LocalHospital style={{ fontSize: '3.5rem', color: '#4CAF50' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Health Management
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Comprehensive health management with vaccination schedules, veterinary integration, and automated alerts
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Vaccination Tracking" className="agri-feature-chip" />
                        <Chip label="Vet Integration" className="agri-feature-chip" />
                        <Chip label="Health Alerts" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Financial Management Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#F57C00"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #F57C00, #FF9800)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <TrendingUp style={{ fontSize: '3.5rem', color: '#F57C00' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Financial Control
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Complete financial control with expense tracking, revenue management, and budget forecasting
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Expense Tracking" className="agri-feature-chip" />
                        <Chip label="Tax Reports" className="agri-feature-chip" />
                        <Chip label="Budget Forecasts" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Feed & Nutrition Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#8B5CF6"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #8B5CF6, #7C3AED)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Restaurant style={{ fontSize: '3.5rem', color: '#8B5CF6' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Feed & Nutrition
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Optimized nutrition management with custom feeding schedules and conversion tracking
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Custom Schedules" className="agri-feature-chip" />
                        <Chip label="Nutrition Tracking" className="agri-feature-chip" />
                        <Chip label="Feed Inventory" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Smart Technology Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#00D4FF"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #00D4FF, #0099CC)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Memory style={{ fontSize: '3.5rem', color: '#00D4FF' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Smart Technology
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        IoT & AI-powered farm management with smart sensors and predictive analytics
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="IoT Sensors" className="agri-feature-chip" />
                        <Chip label="AI Insights" className="agri-feature-chip" />
                        <Chip label="Mobile App" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>
              </motion.div>
            </Box>
          </Box>
        </TabPanel>





        {/* Features Tab - COMPREHENSIVE FEATURE SHOWCASE */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ textAlign: 'center', maxWidth: '1200px', mx: 'auto' }}>
            <Typography
              variant="h2"
              sx={{
                color: 'white',
                textShadow: '3px 3px 6px rgba(0, 0, 0, 0.9)',
                fontWeight: 800,
                fontSize: { xs: '2.5rem', md: '3rem' },
                mb: 2
              }}
            >
              AgriIntel Features
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'white',
                textShadow: '1px 1px 3px rgba(0, 0, 0, 0.7)',
                fontSize: '1.3rem',
                mb: 4,
                maxWidth: '700px',
                mx: 'auto'
              }}
            >
              Comprehensive livestock management tools designed for African farming excellence
            </Typography>

            {/* Features Cards */}
            <Box className="agri-features-horizontal-container">
              <motion.div
                className="agri-features-horizontal-scroll"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                {/* Animal Tracking Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#4CAF50"
                    intensity={0.7}
                    borderGradient="linear-gradient(135deg, #4CAF50, #2E7D32)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Pets style={{ fontSize: '3.5rem', color: '#4CAF50' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Animal Tracking
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Digital livestock management with comprehensive animal profiles, breeding records, and performance tracking for optimal herd management
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Individual Profiles" className="agri-feature-chip" />
                        <Chip label="Breeding Records" className="agri-feature-chip" />
                        <Chip label="Performance Metrics" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Health Monitoring Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#FF5722"
                    intensity={0.7}
                    borderGradient="linear-gradient(135deg, #FF5722, #D84315)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <LocalHospital style={{ fontSize: '3.5rem', color: '#FF5722' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Health Monitoring
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Advanced veterinary management with vaccination schedules, treatment tracking, and early disease detection for healthier livestock
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Vaccination Tracking" className="agri-feature-chip" />
                        <Chip label="Treatment Records" className="agri-feature-chip" />
                        <Chip label="Health Alerts" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Financial Management Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#2196F3"
                    intensity={0.7}
                    borderGradient="linear-gradient(135deg, #2196F3, #1565C0)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <AccountBalance style={{ fontSize: '3.5rem', color: '#2196F3' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Financial Management
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Comprehensive farm financial tracking with expense management, profit analysis, and automated reporting for better business decisions
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Expense Tracking" className="agri-feature-chip" />
                        <Chip label="Profit Analysis" className="agri-feature-chip" />
                        <Chip label="Financial Reports" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Feeding Management Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#FF9800"
                    intensity={0.7}
                    borderGradient="linear-gradient(135deg, #FF9800, #F57C00)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Restaurant style={{ fontSize: '3.5rem', color: '#FF9800' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Feeding Management
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Smart feeding schedules with nutrition tracking, feed inventory management, and automated feeding reminders for optimal animal growth
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Custom Schedules" className="agri-feature-chip" />
                        <Chip label="Nutrition Tracking" className="agri-feature-chip" />
                        <Chip label="Feed Inventory" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Smart Technology Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#00D4FF"
                    intensity={0.7}
                    borderGradient="linear-gradient(135deg, #00D4FF, #0099CC)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Memory style={{ fontSize: '3.5rem', color: '#00D4FF' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Smart Technology
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        IoT & AI-powered farm management with smart sensors, predictive analytics, and automated insights for data-driven farming decisions
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="IoT Sensors" className="agri-feature-chip" />
                        <Chip label="AI Insights" className="agri-feature-chip" />
                        <Chip label="Mobile App" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* WhatsApp Integration Card */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#25D366"
                    intensity={0.7}
                    borderGradient="linear-gradient(135deg, #25D366, #128C7E)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <WhatsApp style={{ fontSize: '3.5rem', color: '#25D366' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        WhatsApp Integration
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Seamless communication and support via WhatsApp with instant notifications, alerts, and direct access to our agricultural experts
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Instant Alerts" className="agri-feature-chip" />
                        <Chip label="Expert Support" className="agri-feature-chip" />
                        <Chip label="Real-time Updates" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>
              </motion.div>
            </Box>
          </Box>
        </TabPanel>

        {/* Features Tab - COMPREHENSIVE FEATURE SHOWCASE */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ textAlign: 'center', maxWidth: '1200px', mx: 'auto' }}>
            <Typography
              variant="h2"
              sx={{
                color: 'white',
                textShadow: '3px 3px 6px rgba(0, 0, 0, 0.9)',
                fontWeight: 800,
                fontSize: { xs: '2.5rem', md: '3rem' },
                mb: 2
              }}
            >
              Powerful Features for Modern Farming
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'white',
                textShadow: '1px 1px 3px rgba(0, 0, 0, 0.7)',
                fontSize: '1.3rem',
                mb: 4,
                maxWidth: '800px',
                mx: 'auto'
              }}
            >
              Everything you need to manage your livestock operation efficiently and profitably
            </Typography>

            {/* Feature Categories */}
            <Box className="agri-features-horizontal-container">
              <motion.div
                className="agri-features-horizontal-scroll"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                {/* Dashboard & Analytics */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#1565C0"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #1565C0, #1976D2)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <TrendingUp style={{ fontSize: '3.5rem', color: '#1565C0' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Smart Dashboard
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Real-time analytics, performance metrics, and AI-powered insights for data-driven decisions
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Real-time Stats" className="agri-feature-chip" />
                        <Chip label="Custom Reports" className="agri-feature-chip" />
                        <Chip label="AI Analytics" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Animal Management */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#4CAF50"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #4CAF50, #2E7D32)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Pets style={{ fontSize: '3.5rem', color: '#4CAF50' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Animal Management
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Complete animal profiles, RFID tracking, genealogy, and growth monitoring
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Individual Profiles" className="agri-feature-chip" />
                        <Chip label="RFID Support" className="agri-feature-chip" />
                        <Chip label="Growth Tracking" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Health & Welfare */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#F44336"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #F44336, #D32F2F)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <LocalHospital style={{ fontSize: '3.5rem', color: '#F44336' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Health & Welfare
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Vaccination schedules, health monitoring, disease tracking, and veterinary management
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Vaccinations" className="agri-feature-chip" />
                        <Chip label="Health Alerts" className="agri-feature-chip" />
                        <Chip label="Vet Records" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Breeding Management */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#9C27B0"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #9C27B0, #673AB7)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Typography
                          variant="h3"
                          sx={{
                            fontSize: '3.5rem',
                            mb: 2
                          }}
                        >
                          🧬
                        </Typography>
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Breeding Programs
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Heat detection, pregnancy tracking, genetic analysis, and artificial insemination management
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Heat Detection" className="agri-feature-chip" />
                        <Chip label="Pregnancy Tracking" className="agri-feature-chip" />
                        <Chip label="Genetic Analysis" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Financial Management */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#FF9800"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #FF9800, #F57C00)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <AccountBalance style={{ fontSize: '3.5rem', color: '#FF9800' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Financial Tracking
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Cost tracking, profit analysis, budget management, and financial reporting
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Cost Tracking" className="agri-feature-chip" />
                        <Chip label="Profit Analysis" className="agri-feature-chip" />
                        <Chip label="Budget Planning" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Feed Management */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#795548"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #795548, #5D4037)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Restaurant style={{ fontSize: '3.5rem', color: '#795548' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Feed Management
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Feed inventory, nutrition planning, consumption tracking, and optimization recommendations
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Inventory Control" className="agri-feature-chip" />
                        <Chip label="Nutrition Plans" className="agri-feature-chip" />
                        <Chip label="Cost Optimization" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* AI & Automation */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.7 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#607D8B"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #607D8B, #455A64)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Memory style={{ fontSize: '3.5rem', color: '#607D8B' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        AI & Automation
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Predictive analytics, automated alerts, voice commands, and intelligent recommendations
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Predictive Analytics" className="agri-feature-chip" />
                        <Chip label="Smart Alerts" className="agri-feature-chip" />
                        <Chip label="Voice Commands" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Service Marketplace */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#E91E63"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #E91E63, #C2185B)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Typography
                          variant="h3"
                          sx={{
                            fontSize: '3.5rem',
                            mb: 2
                          }}
                        >
                          🏪
                        </Typography>
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Service Marketplace
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Connect with veterinarians, suppliers, and service providers with integrated bidding and payments
                      </Typography>
                      <Box className="agri-feature-highlights">
                        <Chip label="Vet Network" className="agri-feature-chip" />
                        <Chip label="Supplier Connect" className="agri-feature-chip" />
                        <Chip label="Integrated Payments" className="agri-feature-chip" />
                      </Box>
                    </Box>
                  </HolographicCard>
                </motion.div>
              </motion.div>
            </Box>

            {/* Feature Tiers Comparison */}
            <Box sx={{ mt: 6, textAlign: 'center' }}>
              <Typography
                variant="h4"
                sx={{
                  color: 'white',
                  textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)',
                  fontWeight: 700,
                  mb: 3
                }}
              >
                Choose Your Plan
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 4, flexWrap: 'wrap' }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Chip
                    label="BETA Trial - 5 Modules"
                    sx={{
                      background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)',
                      color: 'white',
                      fontWeight: 700,
                      px: 2,
                      py: 1
                    }}
                  />
                  <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)', mt: 1 }}>
                    Perfect for small farms (up to 49 animals)
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Chip
                    label="Professional - All 12 Modules"
                    sx={{
                      background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
                      color: 'white',
                      fontWeight: 700,
                      px: 2,
                      py: 1
                    }}
                  />
                  <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)', mt: 1 }}>
                    Complete solution with AI automation
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Chip
                    label="Enterprise - Custom Solutions"
                    sx={{
                      background: 'linear-gradient(135deg, #9C27B0 0%, #673AB7 100%)',
                      color: 'white',
                      fontWeight: 700,
                      px: 2,
                      py: 1
                    }}
                  />
                  <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)', mt: 1 }}>
                    Multi-farm operations with dedicated support
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </TabPanel>

        {/* Testimonials Tab - RICH TESTIMONIAL CARDS */}
        <TabPanel value={tabValue} index={2}>
          <TestimonialsSection />
        </TabPanel>





        {/* Trust & Security Tab - COMPREHENSIVE CONTENT */}
        <TabPanel value={tabValue} index={3}>
          <TrustSignalsSection />
        </TabPanel>

        {/* FAQ Tab - COMPREHENSIVE CONTENT */}
        <TabPanel value={tabValue} index={4}>
          <FAQSection />
        </TabPanel>

        {/* Contact Tab - COMPREHENSIVE CONTENT */}
        <TabPanel value={tabValue} index={5}>
          <Box sx={{ textAlign: 'center', maxWidth: '1200px', mx: 'auto' }}>
            <Typography
              variant="h2"
              sx={{
                color: 'white',
                textShadow: '3px 3px 6px rgba(0, 0, 0, 0.9)',
                fontWeight: 800,
                fontSize: { xs: '2.5rem', md: '3rem' },
                mb: 2
              }}
            >
              Get in Touch with AgriIntel
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'white',
                textShadow: '1px 1px 3px rgba(0, 0, 0, 0.7)',
                fontSize: '1.3rem',
                mb: 4,
                maxWidth: '600px',
                mx: 'auto'
              }}
            >
              Connect with our team of agricultural experts for personalized support
            </Typography>

            {/* Contact Options Cards */}
            <Box className="agri-features-horizontal-container">
              <motion.div
                className="agri-features-horizontal-scroll"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                {/* WhatsApp Contact */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#25D366"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #25D366, #128C7E)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <WhatsApp style={{ fontSize: '3.5rem', color: '#25D366' }} />
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        WhatsApp Support
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Get instant support via WhatsApp for quick questions and assistance
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 1,
                          fontWeight: 600
                        }}
                      >
                        Primary: 082 990 8204
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 3,
                          fontWeight: 600
                        }}
                      >
                        Secondary: ************
                      </Typography>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={() => window.open('https://wa.me/27829908204?text=Hello%20AgriIntel%20team%2C%20I%27m%20interested%20in%20learning%20more%20about%20your%20livestock%20management%20solutions.', '_blank')}
                        sx={{
                          background: 'linear-gradient(135deg, #25D366 0%, #128C7E 100%)',
                          color: 'white',
                          fontWeight: 700,
                          py: 1.5,
                          borderRadius: '12px',
                          '&:hover': {
                            background: 'linear-gradient(135deg, #20B858 0%, #0F6B5C 100%)',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 8px 25px rgba(37, 211, 102, 0.4)'
                          }
                        }}
                      >
                        Chat on WhatsApp
                      </Button>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Email Contact */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#1565C0"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #1565C0, #2196F3)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Typography
                          variant="h3"
                          sx={{
                            fontSize: '3.5rem',
                            mb: 2
                          }}
                        >
                          📧
                        </Typography>
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Email Support
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Send detailed inquiries and get comprehensive responses from our team
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 3,
                          fontWeight: 600
                        }}
                      >
                        <EMAIL>
                      </Typography>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={() => window.open('mailto:<EMAIL>?subject=AgriIntel%20Inquiry&body=Hello%20AgriIntel%20Team%2C%0A%0AI%27m%20interested%20in%20learning%20more%20about%20your%20livestock%20management%20platform.%0A%0AFarm%20Details%3A%0A-%20Farm%20Name%3A%20%0A-%20Location%3A%20%0A-%20Animal%20Count%3A%20%0A-%20Current%20Challenges%3A%20%0A%0APlease%20contact%20me%20to%20discuss%20further.%0A%0AThank%20you%21', '_blank')}
                        sx={{
                          background: 'linear-gradient(135deg, #1565C0 0%, #2196F3 100%)',
                          color: 'white',
                          fontWeight: 700,
                          py: 1.5,
                          borderRadius: '12px',
                          '&:hover': {
                            background: 'linear-gradient(135deg, #0D47A1 0%, #1565C0 100%)',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 8px 25px rgba(21, 101, 192, 0.4)'
                          }
                        }}
                      >
                        Send Email
                      </Button>
                    </Box>
                  </HolographicCard>
                </motion.div>

                {/* Demo Request */}
                <motion.div
                  className="agri-feature-horizontal-card"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  <HolographicCard
                    glowColor="#F57C00"
                    intensity={0.6}
                    borderGradient="linear-gradient(135deg, #F57C00, #FF9800)"
                  >
                    <Box className="agri-feature-content-horizontal">
                      <div className="agri-feature-icon-large">
                        <Typography
                          variant="h3"
                          sx={{
                            fontSize: '3.5rem',
                            mb: 2
                          }}
                        >
                          🎯
                        </Typography>
                      </div>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          color: 'white',
                          mb: 2,
                          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                        }}
                      >
                        Request Demo
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 2,
                          lineHeight: 1.6
                        }}
                      >
                        Schedule a personalized demo to see AgriIntel in action for your farm
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 3,
                          fontWeight: 600
                        }}
                      >
                        Available: Mon-Fri 8AM-5PM
                      </Typography>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={() => window.open('http://localhost:3002/login', '_blank')}
                        sx={{
                          background: 'linear-gradient(135deg, #F57C00 0%, #FF9800 100%)',
                          color: 'white',
                          fontWeight: 700,
                          py: 1.5,
                          borderRadius: '12px',
                          '&:hover': {
                            background: 'linear-gradient(135deg, #E65100 0%, #F57C00 100%)',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 8px 25px rgba(245, 124, 0, 0.4)'
                          }
                        }}
                      >
                        Try Demo Now
                      </Button>
                    </Box>
                  </HolographicCard>
                </motion.div>
              </motion.div>
            </Box>
          </Box>
        </TabPanel>

      </Box>

      {/* Professional Footer - Positioned at bottom */}
      <Box
        component="footer"
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'linear-gradient(135deg, rgba(21, 101, 192, 0.9) 0%, rgba(46, 125, 50, 0.9) 100%)',
          backdropFilter: 'blur(10px)',
          borderTop: '1px solid rgba(255, 255, 255, 0.2)',
          py: 2,
          px: 3
        }}
      >
        <Container maxWidth="xl">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
            {/* Left - Brand */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 800,
                  color: 'white',
                  textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                }}
              >
                AgriIntel
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: 'rgba(255, 255, 255, 0.9)',
                  textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)'
                }}
              >
                © 2025 AgriIntel (Pty) Ltd. All rights reserved.
              </Typography>
            </Box>

            {/* Center - Quick Links */}
            <Box sx={{ display: 'flex', gap: 3, alignItems: 'center' }}>
              <Button
                onClick={() => setTabValue(1)}
                sx={{
                  color: 'white',
                  textTransform: 'none',
                  fontWeight: 600,
                  '&:hover': { color: '#F57C00' }
                }}
              >
                Features
              </Button>
              <Button
                onClick={() => setTabValue(2)}
                sx={{
                  color: 'white',
                  textTransform: 'none',
                  fontWeight: 600,
                  '&:hover': { color: '#F57C00' }
                }}
              >
                Testimonials
              </Button>
              <Button
                onClick={() => setTabValue(5)}
                sx={{
                  color: 'white',
                  textTransform: 'none',
                  fontWeight: 600,
                  '&:hover': { color: '#F57C00' }
                }}
              >
                Contact
              </Button>
            </Box>

            {/* Right - Contact Icons */}
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button
                onClick={() => window.open('https://wa.me/27829908204?text=Hello%20AgriIntel%20Team%2C%20I%27m%20interested%20in%20learning%20more%20about%20your%20livestock%20management%20platform.', '_blank')}
                sx={{
                  minWidth: 'auto',
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  background: 'linear-gradient(135deg, #25D366 0%, #128C7E 100%)',
                  color: 'white',
                  fontSize: '1.2rem',
                  '&:hover': {
                    transform: 'scale(1.1)',
                    boxShadow: '0 4px 15px rgba(37, 211, 102, 0.4)'
                  }
                }}
              >
                📱
              </Button>
              <Button
                onClick={() => window.open('mailto:<EMAIL>?subject=AgriIntel%20Inquiry', '_blank')}
                sx={{
                  minWidth: 'auto',
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  background: 'linear-gradient(135deg, #1565C0 0%, #1976D2 100%)',
                  color: 'white',
                  fontSize: '1.2rem',
                  '&:hover': {
                    transform: 'scale(1.1)',
                    boxShadow: '0 4px 15px rgba(21, 101, 192, 0.4)'
                  }
                }}
              >
                📧
              </Button>
              <Typography
                variant="body2"
                sx={{
                  color: 'rgba(255, 255, 255, 0.9)',
                  textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)',
                  fontSize: '0.9rem'
                }}
              >
                🔒 POPIA Compliant | ISO Certified
              </Typography>
            </Box>
          </Box>
        </Container>
      </Box>
    </main>
  );
};

export default AgriIntelLanding;
