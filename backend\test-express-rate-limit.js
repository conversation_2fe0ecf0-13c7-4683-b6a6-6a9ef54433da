// Test if express-rate-limit can be imported
try {
  const rateLimit = require('express-rate-limit');
  console.log('✅ express-rate-limit imported successfully');
  console.log('Version:', rateLimit.constructor.name);
  
  // Test creating a limiter
  const testLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 5,
    message: 'Test rate limit'
  });
  
  console.log('✅ Rate limiter created successfully');
  console.log('Type:', typeof testLimiter);
  
} catch (error) {
  console.error('❌ Failed to import express-rate-limit:', error.message);
  console.error('Error code:', error.code);
  console.error('Stack:', error.stack);
}
