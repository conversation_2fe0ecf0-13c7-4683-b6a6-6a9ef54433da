{"name": "agriintel-api", "version": "1.0.0", "description": "API for AgriIntel Livestock Management System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "node scripts/kill-node-processes.js 3001 && nodemon src/server.js", "kill-port": "node scripts/kill-node-processes.js", "test": "jest", "migrate": "node scripts/run-migration.js", "populate-mongodb": "node scripts/populate-mongodb.js", "test-mongodb": "node scripts/test-mongodb-connection.js", "create-admin": "node scripts/create-admin-user.js", "setup-db": "npm run test-mongodb && npm run create-admin && npm run populate-mongodb"}, "keywords": ["livestock", "management", "api", "mssql"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^2.2.0", "cheerio": "^1.0.0", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.4.7", "exceljs": "^4.3.0", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^6.1.5", "joi": "^17.13.3", "jsonwebtoken": "^9.0.0", "mongodb": "^6.16.0", "mongoose": "^8.14.1", "mssql": "^11.0.1", "node-cron": "^3.0.3", "node-fetch": "^2.7.0", "pdfkit": "^0.13.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.2", "winston": "^3.8.2"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^3.1.10", "supertest": "^6.3.3"}}