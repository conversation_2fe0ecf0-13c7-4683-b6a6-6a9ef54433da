/**
 * Validation Schemas
 * 
 * This module provides comprehensive validation schemas for all API endpoints.
 */

const Joi = require('joi');

// Common field patterns
const patterns = {
  objectId: /^[0-9a-fA-F]{24}$/,
  phone: /^\+?[\d\s\-\(\)]+$/,
  username: /^[a-zA-Z0-9_]+$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
};

// Common validation schemas
const common = {
  id: Joi.string().pattern(patterns.objectId).required().messages({
    'string.pattern.base': 'Invalid ID format',
    'any.required': 'ID is required'
  }),
  
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sort: Joi.string().default('createdAt'),
    order: Joi.string().valid('asc', 'desc').default('desc')
  }),

  dateRange: Joi.object({
    startDate: Joi.date().iso(),
    endDate: Joi.date().iso().min(Joi.ref('startDate'))
  }),

  search: Joi.object({
    query: Joi.string().trim().min(1).max(100),
    fields: Joi.array().items(Joi.string().trim())
  })
};

// Authentication schemas
const auth = {
  login: Joi.object({
    username: Joi.string().trim().min(3).max(50).required().messages({
      'string.empty': 'Username is required',
      'string.min': 'Username must be at least 3 characters',
      'string.max': 'Username cannot exceed 50 characters',
      'any.required': 'Username is required'
    }),
    password: Joi.string().min(6).required().messages({
      'string.empty': 'Password is required',
      'string.min': 'Password must be at least 6 characters',
      'any.required': 'Password is required'
    })
  }),

  register: Joi.object({
    username: Joi.string().trim().min(3).max(50).required().pattern(patterns.username).messages({
      'string.empty': 'Username is required',
      'string.min': 'Username must be at least 3 characters',
      'string.max': 'Username cannot exceed 50 characters',
      'string.pattern.base': 'Username can only contain letters, numbers, and underscores',
      'any.required': 'Username is required'
    }),
    email: Joi.string().email().required().messages({
      'string.empty': 'Email is required',
      'string.email': 'Email must be valid',
      'any.required': 'Email is required'
    }),
    password: Joi.string().min(8).pattern(patterns.password).required().messages({
      'string.empty': 'Password is required',
      'string.min': 'Password must be at least 8 characters',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': 'Password is required'
    }),
    firstName: Joi.string().trim().min(1).max(50).required().messages({
      'string.empty': 'First name is required',
      'string.max': 'First name cannot exceed 50 characters',
      'any.required': 'First name is required'
    }),
    lastName: Joi.string().trim().min(1).max(50).required().messages({
      'string.empty': 'Last name is required',
      'string.max': 'Last name cannot exceed 50 characters',
      'any.required': 'Last name is required'
    })
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required().messages({
      'string.empty': 'Current password is required',
      'any.required': 'Current password is required'
    }),
    newPassword: Joi.string().min(8).pattern(patterns.password).required().messages({
      'string.empty': 'New password is required',
      'string.min': 'New password must be at least 8 characters',
      'string.pattern.base': 'New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': 'New password is required'
    })
  })
};

// Animal schemas
const animals = {
  create: Joi.object({
    species: Joi.string().trim().min(1).max(50).required().messages({
      'string.empty': 'Species is required',
      'any.required': 'Species is required'
    }),
    breed: Joi.string().trim().min(1).max(50).required().messages({
      'string.empty': 'Breed is required',
      'any.required': 'Breed is required'
    }),
    gender: Joi.string().valid('Male', 'Female').required().messages({
      'any.only': 'Gender must be either Male or Female',
      'any.required': 'Gender is required'
    }),
    birthDate: Joi.date().max('now').required().messages({
      'date.max': 'Birth date cannot be in the future',
      'any.required': 'Birth date is required'
    }),
    earTag: Joi.string().trim().min(1).max(50).messages({
      'string.empty': 'Ear tag cannot be empty if provided'
    }),
    weight: Joi.number().positive().precision(2).messages({
      'number.positive': 'Weight must be positive',
      'number.precision': 'Weight cannot have more than 2 decimal places'
    }),
    color: Joi.string().trim().max(50),
    status: Joi.string().valid('Active', 'Sold', 'Deceased', 'Missing').default('Active'),
    notes: Joi.string().trim().max(500).allow(''),
    motherTag: Joi.string().trim().max(50).allow(''),
    fatherTag: Joi.string().trim().max(50).allow('')
  }),

  update: Joi.object({
    species: Joi.string().trim().min(1).max(50),
    breed: Joi.string().trim().min(1).max(50),
    gender: Joi.string().valid('Male', 'Female'),
    birthDate: Joi.date().max('now'),
    earTag: Joi.string().trim().min(1).max(50),
    weight: Joi.number().positive().precision(2),
    color: Joi.string().trim().max(50),
    status: Joi.string().valid('Active', 'Sold', 'Deceased', 'Missing'),
    notes: Joi.string().trim().max(500).allow(''),
    motherTag: Joi.string().trim().max(50).allow(''),
    fatherTag: Joi.string().trim().max(50).allow('')
  }).min(1) // At least one field must be provided for update
};

// Health record schemas
const health = {
  create: Joi.object({
    animalId: common.id,
    date: Joi.date().max('now').required().messages({
      'date.max': 'Health record date cannot be in the future',
      'any.required': 'Date is required'
    }),
    type: Joi.string().valid('Vaccination', 'Treatment', 'Checkup', 'Surgery', 'Medication', 'Other').required().messages({
      'any.only': 'Type must be one of: Vaccination, Treatment, Checkup, Surgery, Medication, Other',
      'any.required': 'Type is required'
    }),
    description: Joi.string().trim().min(1).max(500).required().messages({
      'string.empty': 'Description is required',
      'string.min': 'Description cannot be empty',
      'any.required': 'Description is required'
    }),
    veterinarian: Joi.string().trim().max(100),
    medication: Joi.string().trim().max(100),
    dosage: Joi.string().trim().max(100),
    cost: Joi.number().min(0).precision(2).messages({
      'number.min': 'Cost cannot be negative',
      'number.precision': 'Cost cannot have more than 2 decimal places'
    }),
    nextTreatmentDate: Joi.date().min('now'),
    notes: Joi.string().trim().max(500).allow('')
  }),

  update: Joi.object({
    date: Joi.date().max('now'),
    type: Joi.string().valid('Vaccination', 'Treatment', 'Checkup', 'Surgery', 'Medication', 'Other'),
    description: Joi.string().trim().min(1).max(500),
    veterinarian: Joi.string().trim().max(100),
    medication: Joi.string().trim().max(100),
    dosage: Joi.string().trim().max(100),
    cost: Joi.number().min(0).precision(2),
    nextTreatmentDate: Joi.date().min('now'),
    notes: Joi.string().trim().max(500).allow('')
  }).min(1)
};

// Financial transaction schemas
const financial = {
  create: Joi.object({
    type: Joi.string().valid('Income', 'Expense').required().messages({
      'any.only': 'Type must be either Income or Expense',
      'any.required': 'Type is required'
    }),
    category: Joi.string().trim().min(1).max(100).required().messages({
      'string.empty': 'Category is required',
      'any.required': 'Category is required'
    }),
    amount: Joi.number().positive().precision(2).required().messages({
      'number.positive': 'Amount must be positive',
      'number.precision': 'Amount cannot have more than 2 decimal places',
      'any.required': 'Amount is required'
    }),
    date: Joi.date().max('now').required().messages({
      'date.max': 'Transaction date cannot be in the future',
      'any.required': 'Date is required'
    }),
    description: Joi.string().trim().min(1).max(500).required().messages({
      'string.empty': 'Description is required',
      'any.required': 'Description is required'
    }),
    animalId: Joi.string().pattern(patterns.objectId).allow(null),
    paymentMethod: Joi.string().valid('Cash', 'Bank Transfer', 'Check', 'Credit Card', 'Other').default('Cash'),
    reference: Joi.string().trim().max(100).allow(''),
    notes: Joi.string().trim().max(500).allow('')
  }),

  update: Joi.object({
    type: Joi.string().valid('Income', 'Expense'),
    category: Joi.string().trim().min(1).max(100),
    amount: Joi.number().positive().precision(2),
    date: Joi.date().max('now'),
    description: Joi.string().trim().min(1).max(500),
    animalId: Joi.string().pattern(patterns.objectId).allow(null),
    paymentMethod: Joi.string().valid('Cash', 'Bank Transfer', 'Check', 'Credit Card', 'Other'),
    reference: Joi.string().trim().max(100).allow(''),
    notes: Joi.string().trim().max(500).allow('')
  }).min(1)
};

// Feeding record schemas
const feeding = {
  create: Joi.object({
    animalId: common.id,
    date: Joi.date().max('now').required().messages({
      'date.max': 'Feeding date cannot be in the future',
      'any.required': 'Date is required'
    }),
    feedType: Joi.string().trim().min(1).max(100).required().messages({
      'string.empty': 'Feed type is required',
      'any.required': 'Feed type is required'
    }),
    quantity: Joi.number().positive().precision(2).required().messages({
      'number.positive': 'Quantity must be positive',
      'number.precision': 'Quantity cannot have more than 2 decimal places',
      'any.required': 'Quantity is required'
    }),
    unit: Joi.string().valid('kg', 'lbs', 'grams', 'tons', 'bags').default('kg').messages({
      'any.only': 'Unit must be one of: kg, lbs, grams, tons, bags'
    }),
    cost: Joi.number().min(0).precision(2).messages({
      'number.min': 'Cost cannot be negative',
      'number.precision': 'Cost cannot have more than 2 decimal places'
    }),
    notes: Joi.string().trim().max(500).allow('')
  }),

  update: Joi.object({
    date: Joi.date().max('now'),
    feedType: Joi.string().trim().min(1).max(100),
    quantity: Joi.number().positive().precision(2),
    unit: Joi.string().valid('kg', 'lbs', 'grams', 'tons', 'bags'),
    cost: Joi.number().min(0).precision(2),
    notes: Joi.string().trim().max(500).allow('')
  }).min(1)
};

// User management schemas
const users = {
  create: Joi.object({
    username: Joi.string().trim().min(3).max(50).required().pattern(patterns.username).messages({
      'string.empty': 'Username is required',
      'string.min': 'Username must be at least 3 characters',
      'string.max': 'Username cannot exceed 50 characters',
      'string.pattern.base': 'Username can only contain letters, numbers, and underscores',
      'any.required': 'Username is required'
    }),
    email: Joi.string().email().required().messages({
      'string.empty': 'Email is required',
      'string.email': 'Email must be valid',
      'any.required': 'Email is required'
    }),
    firstName: Joi.string().trim().min(1).max(50).required().messages({
      'string.empty': 'First name is required',
      'any.required': 'First name is required'
    }),
    lastName: Joi.string().trim().min(1).max(50).required().messages({
      'string.empty': 'Last name is required',
      'any.required': 'Last name is required'
    }),
    role: Joi.string().valid('admin', 'manager', 'veterinarian', 'staff', 'viewer').default('viewer').messages({
      'any.only': 'Role must be one of: admin, manager, veterinarian, staff, viewer'
    }),
    phoneNumber: Joi.string().pattern(patterns.phone).allow('').messages({
      'string.pattern.base': 'Invalid phone number format'
    }),
    department: Joi.string().trim().max(100).allow(''),
    position: Joi.string().trim().max(100).allow('')
  }),

  update: Joi.object({
    email: Joi.string().email(),
    firstName: Joi.string().trim().min(1).max(50),
    lastName: Joi.string().trim().min(1).max(50),
    role: Joi.string().valid('admin', 'manager', 'veterinarian', 'staff', 'viewer'),
    phoneNumber: Joi.string().pattern(patterns.phone).allow(''),
    department: Joi.string().trim().max(100).allow(''),
    position: Joi.string().trim().max(100).allow(''),
    status: Joi.string().valid('active', 'inactive', 'suspended').default('active')
  }).min(1)
};

module.exports = {
  patterns,
  common,
  auth,
  animals,
  health,
  financial,
  feeding,
  users
};
